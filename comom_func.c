#include"comom_func.h"
#include<math.h>

const double R_REF = 150.0;             // 基准距离 Rref (km)
const double ENGINEERING_MARGIN_DB = -2.0; // 工程裕量 (dB)

/**
 * @brief 计算相对信噪比增益 (ΔSNR)
 * @param mode       当前选择的雷达模式
 * @param g_mode_db  模式权重 (dB)
 * @param gains      包含路径特定增益的结构体
 * @return           计算出的 ΔSNR (dB)
 * 说明：为每种模式计算ΔSNR。
 * 拓展性：要添加新模式，在此处的 switch 语句中添加一个 case 即可。
 */
double calculate_delta_snr(RadarMode mode, double g_mode_db, PathGains gains)
{
    double delta_snr = 0.0;
    int N = 0;/*用以表示简化路径*/
    int penalty_db = 0;/*用以处理猝发测距和lpi下的惩罚增益*/
    switch (mode) {
        case MODE_MONOSTATIC:
            // ΔSNR = G_mode (路径叠加贡献为 0 dB)
            delta_snr = g_mode_db;
            break;
        case MODE_BISTATIC_1T1R:
            // ΔSNR = G_mode + Γ_AB
            N = 1*1;
            //delta_snr = g_mode_db + gains.gamma_ab;
            delta_snr = g_mode_db + 10*log10(N);
            break;
        case MODE_BISTATIC_1T2R:
            // ΔSNR = G_mode + 10*log10(1 + 10^(Γ_AB/10))
            N=1*2;
            //delta_snr = g_mode_db + 10.0 * log10(1.0 + pow(10.0, gains.gamma_ab / 10.0));
            delta_snr = g_mode_db + 10*log10(N);
            break;
        case MODE_BISTATIC_2T2R:
        case MODE_MIMO: // MIMO 与 2T2R 等效
            N=2*2;
            // ΔSNR = G_mode + 10*log10(2 + 10^(Γ_AB/10) + 10^(Γ_BA/10))
            //delta_snr = g_mode_db + 10.0 * log10(2.0 + pow(10.0, gains.gamma_ab / 10.0) + pow(10.0, gains.gamma_ba / 10.0));
            delta_snr = g_mode_db + 10*log10(N);
            break;
        case MODE_BURST:
            penalty_db = 3;
            g_mode_db-penalty_db;
        case MODE_LPI:
            // ΔSNR = G_mode - (penalty)
            penalty_db = 9;
            delta_snr = g_mode_db - penalty_db;
            break;
        default:
            printf("错误：未知的雷达模式！\n");
            delta_snr = g_mode_db; // 返回一个默认值
            break;
    }
    return delta_snr;
}

double calculate_range_eng(double delta_snr, double rcs) {
        // 公式: R90_eng = R_ref * 10^((ΔSNR - 2) / 40) * σ^(1/4)
        double exponent = (delta_snr + ENGINEERING_MARGIN_DB) / 40.0;
        double rcs_factor = pow(rcs, 0.25);
        return R_REF * pow(10.0, exponent) * rcs_factor;
}

int get_ranging_accuracy(int target_count, float detection_distance) 
{
    int accuracy = -1;
    // 规则 1 & 2: 目标数量在 1 到 6 之间
    if (target_count >= 1 && target_count <= 6) {
        if (detection_distance >= 0 && detection_distance <= 150) {
            accuracy= 150; 
        } else if (detection_distance > 150 && detection_distance <= 240) {
            accuracy= 240; 
        }
    }
    // 规则 3 & 4: 目标数量在 6 到 20 之间
    else if (target_count > 6 && target_count <= 20) {
        if (detection_distance >= 0 && detection_distance <= 150) {
            accuracy= 250; 
        } else if (detection_distance > 150 && detection_distance <= 240) {
            accuracy= 300; 
        }
    }
    // 规则 5: 探测距离大于 240km
    if (detection_distance > 240) {
        if (target_count <= 20 && target_count >=1) {
             accuracy= 400; 
        }
    }

    return accuracy;
}

/**
 * @brief 根据目标数量和探测角度计算测角精度
 * * @param target_count 侦测到的目标数量
 * @param detection_angle 探测角度（单位：度）
 * @return float 返回对应的测角精度（单位：mrad）。如果无匹配规则，则返回 -1.0f。
 */
float get_angular_accuracy(int target_count, float detection_angle) {
    float accuracy = -1.0f;

    // 首先判断角度范围 0-10°
    if (detection_angle >= 0 && detection_angle <= 10) {
        if (target_count >= 1 && target_count <= 6) {
            accuracy = 4.0f; 
        } else if (target_count > 6 && target_count <= 20) {
            accuracy = 5.5f; 
        }
    }
    // 判断角度范围 10-45°
    else if (detection_angle > 10 && detection_angle <= 45) {
        if (target_count >= 1 && target_count <= 2) {
            accuracy = 5.5f; 
        } else if (target_count >= 3 && target_count <= 6) {
            accuracy = 7.0f; 
        } else if (target_count > 6 && target_count <= 20) {
            accuracy = 8.0f; 
        }
    }
    // 判断角度范围 45-60°
    else if (detection_angle > 45 && detection_angle <= 60) {
        if (target_count >= 1 && target_count <= 6) {
            accuracy = 8.5f; 
        } else if (target_count > 6 && target_count <= 20) {
            accuracy = 10.0f; 
        }
    }

    return accuracy;
}

float get_aesa_search_time_area(float AzMin, float AzMax, float ElMin, float ElMax)
{
    float searchtime = 0.0f;
    const float AzWidth = 3.0;/*方位向波束宽度为3*/
    const float ElWith = 3.0;/*俯仰向波束宽度为3*/
    float deltaTime = 0.5;/*每个波束驻留时间为t,t=0.5*/
    float search_time = 0.0f;
    float AzSpan = AzMax - AzMin;
    if(AzSpan<0)
    {
        AzSpan = 360 + AzMin - AzMax;
    }
    float ElSpan = ElMax - ElMin;
    if(ElSpan<0)
    {
        ElSpan = 360 + ElMin - ElMax;
    }
    search_time = ceil(AzSpan/AzWidth)*ceil(ElSpan/ElWith)*deltaTime;
    return search_time;

}

float multi_rangeing_accuracy(float R1,float R2)
{
    float multi_accuracy = 0.0f;
    multi_accuracy = 0.5*(R1+R2);
    return multi_accuracy;
}

/*多机雷达测角精度模型*/
float multi_angular_accuracy(float A1,float A2)
{
    float multi_accuracy = 0.0f;
    multi_accuracy = 0.5*(A1+A2);
    return multi_accuracy;
}

/*多机雷达搜索时间计算*/
float multi_search_time(float T1,float T2)
{
    float multi_search_time = 0.0f;
    multi_search_time = max(T1,T2);
    return multi_search_time;
}

TaskClassification parse_sensor_style_cmd(SENSOR_STYLE_CMD style)
{
    TaskClassification c;
    // 默认：按“目标-探测”处理
    c.task_type = TASK_TARGET_DETECTION;
    c.target_op = TARGET_OP_DETECT;

    switch (style) {
        // 区域类任务
        case SENSOR_STYLE_AERA_1_DETECT:
        case SENSOR_AESA_AERA_SINGLE_AESA_LPI:
        case SENSOR_AESA_AERA_SHARE_BISTATIC:
        case SENSOR_AESA_AERA_SHARE_EXTEND:
        case SENSOR_CO_AREA_DETECT:
        case SENSOR_OE_AREA_EXTEND:
            c.task_type = TASK_AREA_DETECTION;
            c.target_op = TARGET_OP_NONE;
            break;

        // 目标-定位（测距/协同定位）
        case SENSOR_OE_TARGET_OE_LEAD_LASERDIS:
        case SENSOR_OE_TARGET_ESM_LEAD_LASERDIS:
        case SENSOR_AESA_TARGET_HG_LEAD_AESADIS:
        case SENSOR_AESA_TARGET_ESM_LEAD_AESADIS:
        case SENSOR_2ESM_SIGNAL_CO_LOCATION:
        case SENSOR_3ESM_SIGNAL_CO_LOCATION:
        case SENSOR_2ESM_PAR_CO_LOCATION:
        case SENSOR_3ESM_PAR_CO_LOCATION:
        case SENSOR_CO_LOCATION:
        case SENSOR_OE_CO_LASERDIS:
            c.task_type = TASK_TARGET_DETECTION;
            c.target_op = TARGET_OP_LOCALIZE;
            break;

        // 目标-探测（含协同/跟踪等，归入“探测”）
        case SENSOR_STYLE_TARGET_1_DETECT:
        case SENSOR_AESA_TARGET_SINGLE_AESA_LPI:
        case SENSOR_AESA_TARGET_SHARE_BISTATIC:
        case SENSOR_AESA_TARGET_SHARE_MIMO:
        case SENSOR_CO_DETECT:
        case SENSOR_AESA_CO_POINT_TRACK:
        case SENSOR_AESA_CO_INTER_SOURCE:
        case SENSOR_ESM_HG_DETECT:
        case SENSOR_ESM_OE_DETECT:
        case SENSOR_ESM_AE_DETECT:
        case SENSOR_AESA_MODULE_DETECT:
            c.task_type = TASK_TARGET_DETECTION;
            c.target_op = TARGET_OP_DETECT;
            break;

        case SENSOR_STYLE_TYPE_NA:
        default:
            // 保持默认：目标-探测
            break;
    }

    return c;
}

