#include"comom_func.h"
#include<math.h>

const double R_REF = 150.0;             // 基准距离 Rref (km)
const double ENGINEERING_MARGIN_DB = -2.0; // 工程裕量 (dB)

/**
 * @brief 计算相对信噪比增益 (ΔSNR)
 * @param mode       当前选择的雷达模式
 * @param g_mode_db  模式权重 (dB)
 * @param gains      包含路径特定增益的结构体
 * @return           计算出的 ΔSNR (dB)
 * 说明：为每种模式计算ΔSNR。
 * 拓展性：要添加新模式，在此处的 switch 语句中添加一个 case 即可。
 */
double calculate_delta_snr(RadarMode mode, double g_mode_db, PathGains gains)
{
    double delta_snr = 0.0;
    int N = 0;/*用以表示简化路径*/
    int penalty_db = 0;/*用以处理猝发测距和lpi下的惩罚增益*/
    switch (mode) {
        case MODE_MONOSTATIC:
            // ΔSNR = G_mode (路径叠加贡献为 0 dB)
            delta_snr = g_mode_db;
            break;
        case MODE_BISTATIC_1T1R:
            // ΔSNR = G_mode + Γ_AB
            N = 1*1;
            //delta_snr = g_mode_db + gains.gamma_ab;
            delta_snr = g_mode_db + 10*log10(N);
            break;
        case MODE_BISTATIC_1T2R:
            // ΔSNR = G_mode + 10*log10(1 + 10^(Γ_AB/10))
            N=1*2;
            //delta_snr = g_mode_db + 10.0 * log10(1.0 + pow(10.0, gains.gamma_ab / 10.0));
            delta_snr = g_mode_db + 10*log10(N);
            break;
        case MODE_BISTATIC_2T2R:
        case MODE_MIMO: // MIMO 与 2T2R 等效
            N=2*2;
            // ΔSNR = G_mode + 10*log10(2 + 10^(Γ_AB/10) + 10^(Γ_BA/10))
            //delta_snr = g_mode_db + 10.0 * log10(2.0 + pow(10.0, gains.gamma_ab / 10.0) + pow(10.0, gains.gamma_ba / 10.0));
            delta_snr = g_mode_db + 10*log10(N);
            break;
        case MODE_BURST:
            penalty_db = 3;
            g_mode_db-penalty_db;
        case MODE_LPI:
            // ΔSNR = G_mode - (penalty)
            penalty_db = 9;
            delta_snr = g_mode_db - penalty_db;
            break;
        default:
            printf("错误：未知的雷达模式！\n");
            delta_snr = g_mode_db; // 返回一个默认值
            break;       
    }
    return delta_snr;
}

double calculate_range_eng(double delta_snr, double rcs) {
        // 公式: R90_eng = R_ref * 10^((ΔSNR - 2) / 40) * σ^(1/4)
        double exponent = (delta_snr + ENGINEERING_MARGIN_DB) / 40.0;
        double rcs_factor = pow(rcs, 0.25);
        return R_REF * pow(10.0, exponent) * rcs_factor;
}
