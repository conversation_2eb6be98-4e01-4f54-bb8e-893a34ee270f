#ifndef __COMMON_FUNC_H__
#define __COMMON_FUNC_H__

#ifdef __cplusplus
extern "C" {
#endif

#include"comom_define.h"

double calculate_range_eng(double delta_snr, double rcs);/*使用增益计算距离*/
double calculate_delta_snr(RadarMode mode, double g_mode_db, PathGains gains);

// 根据 SENSOR_STYLE_CMD 将任务归类为 区域/目标(探测/定位)
TaskClassification parse_sensor_style_cmd(SENSOR_STYLE_CMD style);

/*AE 精度模型*/

/*单机距离精度模型 */
/*target_count 目标数量， detection_distance 检测距离*/
int get_ranging_accuracy(int target_count, float detection_distance);


/*单机精度模型*/
/**
 * @brief 根据目标数量和探测角度计算测角精度
 * * @param target_count 侦测到的目标数量
 * @param detection_angle 探测角度（单位：度）
 * @return float 返回对应的测角精度（单位：mrad）。如果无匹配规则，则返回 -1.0f。
 */
float get_angular_accuracy(int target_count, float detection_angle);


/*单机雷达区域搜索时间*/
float get_aesa_search_time_area(float AzMin, float AzMax, float ElMin, float ElMax);


/*多机雷达距离精度模型*/
float multi_rangeing_accuracy(float R1,float R2);

/*多机雷达测角精度模型*/
float multi_angular_accuracy(float A1,float A2);

/*多机雷达搜索时间计算*/
float multi_search_time(float T1,float T2);


#ifdef __cplusplus
}
#endif

#endif

