#ifndef __COMMON_DEFINE_H__
#define __COMMON_DEFINE_H__ 

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_TASK_NUM 10 //最大任务数
#define MAX_PLAT_NUM 2 //最大平台数

// 任务类型
typedef enum {
    TASK_AREA_DETECTION = 0,    // 区域探测
    TASK_TARGET_DETECTION,      // 目标探测
    TASK_TARGET_JAMMING,        // 目标干扰
    TASK_TYPE_COUNT
} TaskType;

typedef enum {
    MODE_NA = 0, // 未定义
    MODE_MONOSTATIC = 1, // 单发单收
    MODE_BISTATIC_1T1R,  // 收发分置 1T1R
    MODE_BISTATIC_1T2R,  // 一发两收 1T2R
    MODE_BISTATIC_2T2R,  // 两发两收 2T2R
    MODE_MIMO,       // MIMO (2x2)
    MODE_BURST,          // 猝发测距
    MODE_LPI,            // 低截获概率
} RadarMode;

typedef struct {
    double gamma_ab; // 路径 AB 的几何/双基地修正 (dB)
    double gamma_ba; // 路径 BA 的几何/双基地修正 (dB)
    double penalty_db; // 用于 Burst 和 LPI 模式的额外信噪比损失 (dB)
} PathGains;

/*传感器探测任务样式*/
typedef enum{
    SENSOR_STYLE_TYPE_NA = 0,/**/
    SENSOR_STYLE_TARGET_1_DETECT = 1,/*单机探测*/
    SENSOR_AESA_TARGET_SINGLE_AESA_LPI = 2,/*单机AESA LPI*/
    SENSOR_AESA_TARGET_SHARE_BISTATIC = 3,/*雷达协同收发分置*/
    SENSOR_AESA_TARGET_SHARE_MIMO = 4,/*雷达协同MIMO目标探测*/
    SENSOR_OE_TARGET_OE_LEAD_LASERDIS = 5,/*光电引导激光测距*/
    SENSOR_OE_TARGET_ESM_LEAD_LASERDIS = 6,/*ESM引导激光测距*/
    SENSOR_AESA_TARGET_HG_LEAD_AESADIS = 7,/*HGESM引导雷达猝发*/
    SENSOR_AESA_TARGET_ESM_LEAD_AESADIS = 8,/*雷达引导激光测距*/
    SENSOR_2ESM_SIGNAL_CO_LOCATION = 9,/*双机ESM信号级协同定位*/
    SENSOR_3ESM_SIGNAL_CO_LOCATION= 10,/*三机ESM信号级协同定位*/
    SENSOR_2ESM_PAR_CO_LOCATION =11,/*双机ESM参数级协同定位*/
    SENSOR_3ESM_PAR_CO_LOCATION= 12,/*三机ESM参数级协同定位*/
    SENSOR_CO_LOCATION= 13,/*协同定位*/
    SENSOR_CO_DETECT= 14,/*编组探测*/
    SENSOR_AESA_CO_POINT_TRACK= 15,/*雷达点航迹协同*/
    SENSOR_AESA_CO_INTER_SOURCE= 16,/*协同探测跟踪*/
    SENSOR_OE_CO_LASERDIS= 17,/*激光协同测距*/
    SENSOR_STYLE_AERA_1_DETECT= 18,/*单机区域探测*/
    SENSOR_AESA_AERA_SINGLE_AESA_LPI= 19,/*单机雷达LPI区域*/
    SENSOR_AESA_AERA_SHARE_BISTATIC= 20,/*雷达区域协同收发分置*/
    SENSOR_AESA_AERA_SHARE_EXTEND= 21,/*雷达协同增程*/
    SENSOR_ESM_HG_DETECT= 22,/*单机ESM+HG探测*/
    SENSOR_ESM_OE_DETECT= 23,/*单机ESM+光电探测*/
    SENSOR_ESM_AE_DETECT= 24,/*单机HG+雷达分时探测*/
    SENSOR_AESA_MODULE_DETECT= 25,/*单机雷达模板探测*/
    SENSOR_CO_AREA_DETECT=26,/*协同探测区域(分区)*/
    SENSOR_OE_AREA_EXTEND=27,/*光电区域增强*/
}S;



#ifdef __cplusplus
}
#endif

#endif